/**
 * 交易日历控制器
 * 提供交易日历相关的API接口
 */

const { connPG } = require('../db/pg');

/**
 * 获取指定月份的日历数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getCalendarData = async (req, res) => {
  try {
    const { year, month } = req.body;
    
    // 验证参数
    if (!year || !month) {
      return res.status(400).json({ 
        code: 1, 
        msg: '年份和月份参数不能为空' 
      });
    }

    // 构建查询条件
    const startDate = `${year}${month.toString().padStart(2, '0')}01`;
    const endDate = `${year}${month.toString().padStart(2, '0')}31`;

    console.log(`查询日历数据: ${startDate} - ${endDate}`);

    // 查询日历数据
    const query = `
      SELECT
        day,
        dat,
        wrk,
        tra,
        sun,
        mon,
        tue,
        wed,
        thu,
        fri,
        sat,
        str,
        tal,
        spr,
        main_duty_user,
        deputy_duty_user,
        duty_manager,
        simulation_user,
        inspection_user,
        main_duty_name,
        deputy_duty_name,
        duty_manager_name,
        simulation_name,
        inspection_name,
        change_summary,
        change_count
      FROM v_ops_calendar
      WHERE day >= $1 AND day <= $2
      ORDER BY day
    `;

    const result = await connPG.query(query, [startDate, endDate]);

    console.log(`查询结果数量: ${result.rows.length}`);
    if (result.rows.length > 0) {
      console.log('查询结果示例:', result.rows.slice(0, 3));

      // 检查变更数据
      const hasChanges = result.rows.filter(row => row.change_count > 0);
      if (hasChanges.length > 0) {
        console.log('有变更的数据示例:', hasChanges.slice(0, 2));
        console.log('change_count数据类型:', typeof hasChanges[0].change_count);
      }
    }

    // 如果没有数据，生成当月的基础日历数据
    if (result.rows.length === 0) {
      console.log('数据库无数据，生成基础日历数据');
      const generatedData = generateMonthCalendar(year, month);
      console.log('生成的数据示例:', generatedData.slice(0, 3));
      return res.json({
        code: 0,
        msg: generatedData,
        total: generatedData.length
      });
    }

    res.json({
      code: 0,
      msg: result.rows,
      total: result.rows.length
    });
  } catch (error) {
    console.error('获取日历数据失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

/**
 * 获取指定日期的变更详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getDateChanges = async (req, res) => {
  try {
    const { date } = req.body;

    if (!date) {
      return res.status(400).json({
        code: 1,
        msg: '日期参数不能为空'
      });
    }

    console.log(`查询日期变更详情: ${date}`);

    // 查询指定日期的变更信息，包含字典名称和用户真实姓名
    const query = `
      SELECT
        t.id,
        t.change_id,
        t.title,
        t.system,
        t.change_level,
        COALESCE(d.dict_name, t.change_level) AS change_level_name,
        t.planned_change_time,
        t.requester,
        COALESCE(u1.real_name, t.requester) AS requester_name,
        t.implementers,
        (
          SELECT string_agg(COALESCE(u.real_name, usernames.username), ', ')
          FROM (
            SELECT unnest(string_to_array(t.implementers, ',')) AS username
          ) AS usernames
          LEFT JOIN cmdb_users u ON u.username = usernames.username AND u.del_flag = '0'
        ) AS implementers_name,
        t.created_at,
        t.updated_at
      FROM ops_change_management t
      LEFT JOIN cmdb_data_dictionary d ON d.dict_type = 'P' AND d.dict_code = t.change_level AND d.del_flag = '0'
      LEFT JOIN cmdb_users u1 ON u1.username = t.requester AND u1.del_flag = '0'
      WHERE TO_CHAR(t.planned_change_time, 'YYYYMMDD') = $1
      AND t.del_flag = '0'
      ORDER BY t.change_id DESC
    `;

    const result = await connPG.query(query, [date]);

    res.json({
      code: 0,
      msg: result.rows,
      total: result.rows.length
    });
  } catch (error) {
    console.error('获取日期变更详情失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

/**
 * 生成指定月份的基础日历数据
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @returns {Array} 日历数据数组
 */
const generateMonthCalendar = (year, month) => {
  const calendarData = [];
  const daysInMonth = new Date(year, month, 0).getDate();

  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month - 1, day);
    const dayOfWeek = date.getDay(); // 0=周日, 1=周一, ..., 6=周六
    const dateStr = `${year}${month.toString().padStart(2, '0')}${day.toString().padStart(2, '0')}`;

    // 判断是否为工作日（周一到周五）
    const isWorkday = dayOfWeek >= 1 && dayOfWeek <= 5 ? 1 : 0;
    
    calendarData.push({
      day: dateStr,
      dat: day,
      wrk: isWorkday,
      tra: isWorkday, // 默认工作日即为交易日
      sun: dayOfWeek === 0 ? 1 : 0,
      mon: dayOfWeek === 1 ? 1 : 0,
      tue: dayOfWeek === 2 ? 1 : 0,
      wed: dayOfWeek === 3 ? 1 : 0,
      thu: dayOfWeek === 4 ? 1 : 0,
      fri: dayOfWeek === 5 ? 1 : 0,
      sat: dayOfWeek === 6 ? 1 : 0,
      str: day === 1 ? 1 : 0, // 月初
      tal: day === daysInMonth ? 1 : 0, // 月末
      spr: 0, // 春节标记，需要根据实际情况设置
      main_duty_user: null,
      deputy_duty_user: null,
      duty_manager: null,
      simulation_user: null,
      inspection_user: null,
      main_duty_name: null,
      deputy_duty_name: null,
      duty_manager_name: null,
      simulation_name: null,
      inspection_name: null,
      change_summary: '',
      change_count: 0
    });
  }

  return calendarData;
};

/**
 * 获取用户列表（用于值班排班选择）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getUserList = async (req, res) => {
  try {
    console.log('获取用户列表');

    const query = `
      SELECT username, real_name
      FROM cmdb_users
      WHERE del_flag = '0'
      ORDER BY real_name
    `;

    const result = await connPG.query(query);

    res.json({
      code: 0,
      msg: result.rows
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

/**
 * 检查用户是否有值班排班权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const checkDutyPermission = async (req, res) => {
  try {
    const { username } = req.body;

    if (!username) {
      return res.status(400).json({
        code: 1,
        msg: '用户名参数不能为空'
      });
    }

    console.log(`检查用户值班排班权限: ${username}`);

    // admin用户默认有所有权限
    if (username === 'admin') {
      return res.json({
        code: 0,
        msg: {
          hasEditPermission: true,
          hasViewPermission: true
        }
      });
    }

    // 查询用户权限
    const query = `
      SELECT
        p.permission_type,
        u.username,
        u.real_name
      FROM ops_calendar_duty_permissions p
      LEFT JOIN cmdb_users u ON p.user_id = u.id
      WHERE u.username = $1
      AND p.del_flag = '0'
      AND u.del_flag = '0'
    `;

    const result = await connPG.query(query, [username]);

    const permissions = result.rows;
    const hasEditPermission = permissions.some(p => p.permission_type === 'edit');
    const hasViewPermission = permissions.some(p => p.permission_type === 'view' || p.permission_type === 'edit');

    console.log('权限检查结果:', {
      username,
      permissions: permissions.map(p => p.permission_type),
      hasEditPermission,
      hasViewPermission
    });

    res.json({
      code: 0,
      msg: {
        hasEditPermission,
        hasViewPermission
      }
    });
  } catch (error) {
    console.error('检查值班排班权限失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

/**
 * 更新值班排班
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const updateDutySchedule = async (req, res) => {
  try {
    const {
      day,
      main_duty_user,
      deputy_duty_user,
      duty_manager,
      simulation_user,
      inspection_user,
      updated_by
    } = req.body;

    if (!day) {
      return res.status(400).json({
        code: 1,
        msg: '日期参数不能为空'
      });
    }

    // 权限验证：检查当前用户是否有编辑权限
    const currentUsername = updated_by || 'admin';

    // admin用户默认有权限
    if (currentUsername !== 'admin') {
      const permissionQuery = `
        SELECT 1
        FROM ops_calendar_duty_permissions p
        LEFT JOIN cmdb_users u ON p.user_id = u.id
        WHERE u.username = $1
        AND p.permission_type = 'edit'
        AND p.del_flag = '0'
        AND u.del_flag = '0'
      `;

      const permissionResult = await connPG.query(permissionQuery, [currentUsername]);

      if (permissionResult.rows.length === 0) {
        return res.status(403).json({
          code: 1,
          msg: '您没有值班排班编辑权限'
        });
      }
    }

    console.log(`更新值班排班: ${day}, 操作用户: ${currentUsername}`);

    // 检查记录是否存在
    const checkQuery = 'SELECT day FROM ops_calendar WHERE day = $1';
    const checkResult = await connPG.query(checkQuery, [day]);

    let query, params;

    if (checkResult.rows.length > 0) {
      // 更新现有记录的值班信息
      query = `
        UPDATE ops_calendar
        SET
          main_duty_user = $2,
          deputy_duty_user = $3,
          duty_manager = $4,
          simulation_user = $5,
          inspection_user = $6,
          updated_at = CURRENT_TIMESTAMP,
          updated_by = $7
        WHERE day = $1
        RETURNING *
      `;
      params = [day, main_duty_user, deputy_duty_user, duty_manager, simulation_user, inspection_user, updated_by || 'system'];
    } else {
      // 插入新记录（包含基础日历信息）
      const date = new Date(
        parseInt(day.substring(0, 4)),
        parseInt(day.substring(4, 6)) - 1,
        parseInt(day.substring(6, 8))
      );
      const dayOfWeek = date.getDay();
      const dayOfMonth = date.getDate();
      const daysInMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
      const isWorkday = dayOfWeek >= 1 && dayOfWeek <= 5 ? 1 : 0;

      query = `
        INSERT INTO ops_calendar (
          day, dat, wrk, tra, sun, mon, tue, wed, thu, fri, sat, str, tal, spr,
          main_duty_user, deputy_duty_user, duty_manager, simulation_user, inspection_user,
          created_by, updated_by
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14,
          $15, $16, $17, $18, $19, $20, $21
        ) RETURNING *
      `;
      params = [
        day,
        dayOfMonth,
        isWorkday,
        isWorkday,
        dayOfWeek === 0 ? 1 : 0,
        dayOfWeek === 1 ? 1 : 0,
        dayOfWeek === 2 ? 1 : 0,
        dayOfWeek === 3 ? 1 : 0,
        dayOfWeek === 4 ? 1 : 0,
        dayOfWeek === 5 ? 1 : 0,
        dayOfWeek === 6 ? 1 : 0,
        dayOfMonth === 1 ? 1 : 0,
        dayOfMonth === daysInMonth ? 1 : 0,
        0, // spr
        main_duty_user,
        deputy_duty_user,
        duty_manager,
        simulation_user,
        inspection_user,
        updated_by || 'system',
        updated_by || 'system'
      ];
    }

    const result = await connPG.query(query, params);

    res.json({
      code: 0,
      msg: result.rows[0]
    });
  } catch (error) {
    console.error('更新值班排班失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

/**
 * 添加或更新日历数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const updateCalendarData = async (req, res) => {
  try {
    const { day, wrk, tra, spr, updated_by } = req.body;
    
    if (!day) {
      return res.status(400).json({ 
        code: 1, 
        msg: '日期参数不能为空' 
      });
    }

    console.log(`更新日历数据: ${day}`);

    // 检查记录是否存在
    const checkQuery = 'SELECT day FROM ops_calendar WHERE day = $1';
    const checkResult = await connPG.query(checkQuery, [day]);

    let query, params;
    
    if (checkResult.rows.length > 0) {
      // 更新现有记录
      query = `
        UPDATE ops_calendar 
        SET wrk = $2, tra = $3, spr = $4
        WHERE day = $1
        RETURNING *
      `;
      params = [day, wrk, tra, spr];
    } else {
      // 插入新记录
      const date = new Date(
        parseInt(day.substring(0, 4)),
        parseInt(day.substring(4, 6)) - 1,
        parseInt(day.substring(6, 8))
      );
      const dayOfWeek = date.getDay();
      const dayOfMonth = date.getDate();
      const daysInMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();

      query = `
        INSERT INTO ops_calendar (
          day, dat, wrk, tra, sun, mon, tue, wed, thu, fri, sat, str, tal, spr
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
        ) RETURNING *
      `;
      params = [
        day,
        dayOfMonth,
        wrk,
        tra,
        dayOfWeek === 0 ? 1 : 0,
        dayOfWeek === 1 ? 1 : 0,
        dayOfWeek === 2 ? 1 : 0,
        dayOfWeek === 3 ? 1 : 0,
        dayOfWeek === 4 ? 1 : 0,
        dayOfWeek === 5 ? 1 : 0,
        dayOfWeek === 6 ? 1 : 0,
        dayOfMonth === 1 ? 1 : 0,
        dayOfMonth === daysInMonth ? 1 : 0,
        spr
      ];
    }

    const result = await connPG.query(query, params);

    res.json({
      code: 0,
      msg: result.rows[0]
    });
  } catch (error) {
    console.error('更新日历数据失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

/**
 * 更新用户的交易日历权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const updateDutyPermission = async (req, res) => {
  try {
    const { username, permission_type, updated_by } = req.body;

    if (!username) {
      return res.status(400).json({
        code: 1,
        msg: '用户名参数不能为空'
      });
    }

    // 只有admin用户可以设置权限
    if (updated_by !== 'admin') {
      return res.status(403).json({
        code: 1,
        msg: '只有管理员可以设置权限'
      });
    }

    console.log(`更新用户权限: ${username}, 权限类型: ${permission_type}`);

    // 获取用户ID
    const userQuery = `
      SELECT id FROM cmdb_users
      WHERE username = $1 AND del_flag = '0'
    `;
    const userResult = await connPG.query(userQuery, [username]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        code: 1,
        msg: '用户不存在'
      });
    }

    const userId = userResult.rows[0].id;

    // 先删除用户的所有交易日历权限（软删除）
    await connPG.query(
      `UPDATE ops_calendar_duty_permissions
       SET del_flag = '1', updated_at = CURRENT_TIMESTAMP, updated_by = $1
       WHERE user_id = $2 AND del_flag = '0'`,
      [updated_by, userId]
    );

    console.log(`已清除用户 ${username} 的所有权限`);

    // 如果权限类型不为空，则添加新权限
    if (permission_type && permission_type.trim() !== '') {
      // 验证权限类型
      if (!['view', 'edit'].includes(permission_type)) {
        return res.status(400).json({
          code: 1,
          msg: '无效的权限类型，只支持 view 或 edit'
        });
      }

      // 检查是否已存在相同权限的记录
      const checkQuery = `
        SELECT id FROM ops_calendar_duty_permissions
        WHERE user_id = $1 AND permission_type = $2
      `;
      const checkResult = await connPG.query(checkQuery, [userId, permission_type]);

      if (checkResult.rows.length > 0) {
        // 恢复已存在的记录
        await connPG.query(
          `UPDATE ops_calendar_duty_permissions
           SET del_flag = '0', updated_at = CURRENT_TIMESTAMP, updated_by = $1
           WHERE user_id = $2 AND permission_type = $3`,
          [updated_by, userId, permission_type]
        );
        console.log(`恢复用户 ${username} 的 ${permission_type} 权限`);
      } else {
        // 插入新记录
        await connPG.query(
          `INSERT INTO ops_calendar_duty_permissions
           (user_id, permission_type, created_by, updated_by, del_flag)
           VALUES ($1, $2, $3, $4, '0')`,
          [userId, permission_type, updated_by, updated_by]
        );
        console.log(`新增用户 ${username} 的 ${permission_type} 权限`);
      }
    } else {
      console.log(`用户 ${username} 被设置为无权限`);
    }

    res.json({
      code: 0,
      msg: '权限更新成功'
    });
  } catch (error) {
    console.error('更新权限失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

module.exports = {
  getCalendarData,
  getDateChanges,
  getUserList,
  updateDutySchedule,
  updateCalendarData,
  checkDutyPermission,
  updateDutyPermission
};
