<template>
  <div class="calendar-container">
    <!-- 日历主体 -->
    <el-card class="table-card" shadow="hover" v-loading="loading">
      <template #header>
        <div class="card-header">
        <el-alert
          title="操作提示"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <div style="font-size: 13px; line-height: 1.6;">
              <strong>左右键点击说明：</strong>变更和值班同时存在时，左键查看变更详情，右键编辑值班排班<br>
            </div>
          </template>
        </el-alert>
          <div class="header-left">
            <span class="card-title">{{ currentMonthTitle }}</span>
            <el-tag v-if="monthChangeCount > 0" type="primary" class="month-tag">
              本月共 {{ monthChangeCount }} 个变更
            </el-tag>
          </div>

          <div class="header-center">
            <div class="legend">
              <span class="legend-item">
                <span class="legend-color workday"></span>
                工作日
              </span>
              <span class="legend-item">
                <span class="legend-color weekend"></span>
                非工作日
              </span>
              <span class="legend-item">
                <span class="legend-color has-change"></span>
                有变更
              </span>
            </div>
          </div>

          <div class="header-right">
            <el-form-item label="选择月份：" class="form-item-inline">
              <div class="month-selector">
                <el-button
                  size="small"
                  @click="goToPreviousMonth"
                  :disabled="loading"
                  title="上个月"
                >
                  ‹
                </el-button>
                <el-date-picker
                  v-model="currentDate"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY年MM月"
                  value-format="YYYY-MM"
                  @change="handleDateChange"
                  style="width: 160px; margin: 0 8px;"
                />
                <el-button
                  size="small"
                  @click="goToNextMonth"
                  :disabled="loading"
                  title="下个月"
                >
                  ›
                </el-button>
              </div>
            </el-form-item>
            <el-button
              type="primary"
              :icon="Refresh"
              @click="refreshCalendar"
              :loading="loading"
            >
              刷新
            </el-button>
            <el-button
              type="success"
              @click="goToCurrentMonth"
              :disabled="loading"
              title="回到当前月"
            >
              当前月
            </el-button>
            
            <!-- 调试按钮 -->
            <!-- <el-button
              type="info"
              size="small"
              @click="debugPermissions"
            >
              调试权限
            </el-button> -->
          </div>
        </div>
      </template>

      <div class="calendar-grid">
        <!-- 星期标题 -->
        <div class="week-header">
          <div class="week-day">日</div>
          <div class="week-day">一</div>
          <div class="week-day">二</div>
          <div class="week-day">三</div>
          <div class="week-day">四</div>
          <div class="week-day">五</div>
          <div class="week-day">六</div>
        </div>

        <!-- 日历格子 -->
        <div class="calendar-body">
          <div
            v-for="day in calendarDays"
            :key="day.date"
            :class="getDayClass(day)"
            @click="handleDayClick(day)"
            @contextmenu.prevent="handleDayRightClick(day)"
          >
            <div class="day-header">
              <div class="day-number">{{ day.dayNumber }}</div>
              <!-- 变更角标 -->
              <div v-if="day.changeCount > 0" class="change-badge-corner" :title="`${day.changeCount}个变更: ${day.changeSummary}`">
                {{ day.changeCount }}
              </div>
            </div>
            <div class="day-content">
              <!-- 值班信息 -->
              <div v-if="day.isCurrentMonth && (day.mainDutyName || day.deputyDutyName || day.dutyManagerName || day.simulationName || day.inspectionName)" class="duty-info">
                <div v-if="day.mainDutyName" class="duty-item main-duty" :title="`主班: ${day.mainDutyName}`">
                  主班: {{ day.mainDutyName }}
                </div>
                <div v-if="day.deputyDutyName" class="duty-item deputy-duty" :title="`副班: ${day.deputyDutyName}`">
                  副班: {{ day.deputyDutyName }}
                </div>
                <div v-if="day.dutyManagerName" class="duty-item manager-duty" :title="`值班经理: ${day.dutyManagerName}`">
                  经理: {{ day.dutyManagerName }}
                </div>
                <div v-if="day.simulationName" class="duty-item simulation-duty" :title="`仿真: ${day.simulationName}`">
                  仿真: {{ day.simulationName }}
                </div>
                <div v-if="day.inspectionName" class="duty-item inspection-duty" :title="`巡检: ${day.inspectionName}`">
                  巡检: {{ day.inspectionName }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 变更详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="`${selectedDateFormatted} 变更详情`"
      width="85%"
      top="5vh"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <div class="dialog-header-info">
          <el-tag type="info" size="large">
            共找到 {{ selectedDateChanges.length }} 个变更
          </el-tag>
        </div>

        <el-table
          :data="selectedDateChanges"
          stripe
          style="width: 100%"
          v-loading="dialogLoading"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
        >
          <el-table-column prop="change_id" label="变更编号" width="150" show-overflow-tooltip />
          <el-table-column prop="title" label="变更名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="system" label="所属系统" width="120" show-overflow-tooltip />
          <el-table-column label="变更级别" width="100">
            <template #default="scope">
              {{ scope.row.change_level_name || scope.row.change_level }}
            </template>
          </el-table-column>
          <el-table-column label="变更负责人" width="120">
            <template #default="scope">
              {{ scope.row.requester_name || scope.row.requester }}
            </template>
          </el-table-column>
          <el-table-column label="实施人" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.implementers_name || scope.row.implementers }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                link
                size="small"
                @click="viewChangeDetail(scope.row)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 值班排班对话框 -->
    <el-dialog
      v-model="dutyDialogVisible"
      :title="`${selectedDateFormatted} 值班排班`"
      width="600px"
      :close-on-click-modal="false"
    >
      <!-- 权限提示 -->
      <el-alert
        v-if="hasViewPermission && !hasEditPermission"
        title="您只有查看权限，无法修改值班排班"
        type="warning"
        :closable="false"
        style="margin-bottom: 16px;"
      />

      <!-- 无权限提示 -->
      <el-alert
        v-if="!hasViewPermission && !hasEditPermission"
        title="您没有交易日历的编辑权限"
        type="error"
        :closable="false"
        style="margin-bottom: 16px;"
      />

      <el-form
        :model="dutyForm"
        label-width="100px"
        class="duty-form"
      >
        <el-form-item label="主班人员：">
          <el-select
            v-model="dutyForm.main_duty_user"
            placeholder="请选择主班人员"
            clearable
            filterable
            style="width: 100%"
            :disabled="!hasEditPermission"
          >
            <el-option
              v-for="user in userList"
              :key="user.username"
              :label="user.real_name"
              :value="user.username"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="副班人员：">
          <el-select
            v-model="dutyForm.deputy_duty_user"
            placeholder="请选择副班人员"
            clearable
            filterable
            style="width: 100%"
            :disabled="!hasEditPermission"
          >
            <el-option
              v-for="user in userList"
              :key="user.username"
              :label="user.real_name"
              :value="user.username"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="值班经理：">
          <el-select
            v-model="dutyForm.duty_manager"
            placeholder="请选择值班经理"
            clearable
            filterable
            style="width: 100%"
            :disabled="!hasEditPermission"
          >
            <el-option
              v-for="user in userList"
              :key="user.username"
              :label="user.real_name"
              :value="user.username"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="仿真人员：">
          <el-select
            v-model="dutyForm.simulation_user"
            placeholder="请选择仿真人员"
            clearable
            filterable
            style="width: 100%"
            :disabled="!hasEditPermission"
          >
            <el-option
              v-for="user in userList"
              :key="user.username"
              :label="user.real_name"
              :value="user.username"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="巡检人员：">
          <el-select
            v-model="dutyForm.inspection_user"
            placeholder="请选择巡检人员"
            clearable
            filterable
            style="width: 100%"
            :disabled="!hasEditPermission"
          >
            <el-option
              v-for="user in userList"
              :key="user.username"
              :label="user.real_name"
              :value="user.username"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dutyDialogVisible = false">
            {{ hasEditPermission ? '取消' : '关闭' }}
          </el-button>
          <el-button
            v-if="hasEditPermission"
            type="primary"
            @click="saveDutySchedule"
            :loading="dutyLoading"
          >
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, computed, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Calendar } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import {
  formatDate,
  formatDateForDB,
  parseToDBFormat,
  testDateConversion
} from '@/utils/dateUtils'

export default {
  name: 'OpsCalendar',
  components: {
    Refresh,
    Calendar
  },
  setup() {
    const router = useRouter()
    const { proxy } = getCurrentInstance()

    // 响应式数据
    const loading = ref(false)
    const dialogLoading = ref(false)
    const dialogVisible = ref(false)
    const dutyDialogVisible = ref(false)
    const dutyLoading = ref(false)
    // 从localStorage获取上次选择的月份，如果没有则使用当前月
    const getInitialDate = () => {
      const savedDate = localStorage.getItem('ops_calendar_selected_month')
      if (savedDate) {
        console.log('从localStorage恢复月份:', savedDate)
        return savedDate
      } else {
        const currentMonth = new Date().toISOString().slice(0, 7)
        console.log('首次访问，使用当前月份:', currentMonth)
        return currentMonth
      }
    }
    const currentDate = ref(getInitialDate()) // YYYY-MM 格式
    const calendarData = ref([])
    const selectedDate = ref('')
    const selectedDateChanges = ref([])
    const userList = ref([])
    const dutyForm = ref({
      main_duty_user: '',
      deputy_duty_user: '',
      duty_manager: '',
      simulation_user: '',
      inspection_user: ''
    })

    // 权限控制
    const hasEditPermission = ref(false)
    const hasViewPermission = ref(false) // 默认无权限，需要通过API检查
    const permissionLoading = ref(false)

    // 计算属性
    const currentMonthTitle = computed(() => {
      const [year, month] = currentDate.value.split('-').map(Number)
      return `${year}年${month}月`
    })

    const selectedDateFormatted = computed(() => {
      if (!selectedDate.value) return ''
      const date = new Date(selectedDate.value)
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
    })

    const monthChangeCount = computed(() => {
      // 只统计当前月份的变更数量
      if (!calendarData.value || calendarData.value.length === 0) {
        return 0
      }

      const [year, month] = currentDate.value.split('-').map(Number)
      const currentMonthPrefix = `${year}${month.toString().padStart(2, '0')}`

      console.log('计算月度变更统计:', {
        currentDate: currentDate.value,
        monthPrefix: currentMonthPrefix,
        calendarDataLength: calendarData.value.length
      })

      let totalChanges = 0

      for (const day of calendarData.value) {
        // 检查是否为当前月份的数据
        if (day.day && typeof day.day === 'string' && day.day.startsWith(currentMonthPrefix)) {
          // 确保 change_count 是数字
          const changeCount = Number(day.change_count) || 0
          totalChanges += changeCount

          if (changeCount > 0) {
            console.log(`找到变更: ${day.day}, 变更数量: ${changeCount} (原始值: ${day.change_count}, 类型: ${typeof day.change_count})`)
          }
        }
      }

      console.log('月度变更总数:', totalChanges)
      return totalChanges
    })

    // 计算当前月份的日历天数
    const calendarDays = computed(() => {
      const [year, month] = currentDate.value.split('-').map(Number)
      const firstDay = new Date(year, month - 1, 1)
      const lastDay = new Date(year, month, 0)
      const daysInMonth = lastDay.getDate()
      const startDayOfWeek = firstDay.getDay() // 0=周日, 1=周一, ..., 6=周六
      
      const days = []
      
      // 添加上个月的尾部天数（用于填充第一周）
      for (let i = startDayOfWeek - 1; i >= 0; i--) {
        const prevDate = new Date(year, month - 1, -i)
        days.push({
          date: formatDate(prevDate),
          dayNumber: prevDate.getDate(),
          isCurrentMonth: false,
          isWorkday: false,
          isHoliday: false,
          changeCount: 0,
          changeSummary: ''
        })
      }
      
      // 添加当前月的天数
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month - 1, day)
        const dateStr = formatDate(date)
        const dbDateStr = formatDateForDB(date)
        const calendarItem = calendarData.value.find(item => item.day === dbDateStr)

        // 调试信息：显示日期匹配情况
        if (day <= 3) {
          console.log(`日期匹配调试 - 日期: ${day}, 前端格式: ${dateStr}, 数据库格式: ${dbDateStr}`)
          console.log(`匹配到的数据:`, calendarItem)
          if (calendarItem) {
            console.log(`工作日标记: wrk=${calendarItem.wrk}, 变更数量: ${calendarItem.change_count}`)
          }
        }
        
        days.push({
          date: dateStr,
          dayNumber: day,
          isCurrentMonth: true,
          isWorkday: calendarItem ? calendarItem.wrk === 1 : (date.getDay() >= 1 && date.getDay() <= 5),
          isHoliday: calendarItem ? calendarItem.spr === 1 : false,
          changeCount: calendarItem ? calendarItem.change_count : 0,
          changeSummary: calendarItem ? calendarItem.change_summary : '',
          // 值班人员信息
          mainDutyUser: calendarItem ? calendarItem.main_duty_user : null,
          deputyDutyUser: calendarItem ? calendarItem.deputy_duty_user : null,
          dutyManager: calendarItem ? calendarItem.duty_manager : null,
          simulationUser: calendarItem ? calendarItem.simulation_user : null,
          inspectionUser: calendarItem ? calendarItem.inspection_user : null,
          mainDutyName: calendarItem ? calendarItem.main_duty_name : null,
          deputyDutyName: calendarItem ? calendarItem.deputy_duty_name : null,
          dutyManagerName: calendarItem ? calendarItem.duty_manager_name : null,
          simulationName: calendarItem ? calendarItem.simulation_name : null,
          inspectionName: calendarItem ? calendarItem.inspection_name : null
        })
      }
      
      // 添加下个月的开头天数（用于填充最后一周）
      const totalCells = Math.ceil(days.length / 7) * 7
      for (let i = days.length; i < totalCells; i++) {
        const nextDate = new Date(year, month, i - days.length + 1)
        days.push({
          date: formatDate(nextDate),
          dayNumber: nextDate.getDate(),
          isCurrentMonth: false,
          isWorkday: false,
          isHoliday: false,
          changeCount: 0,
          changeSummary: ''
        })
      }
      
      return days
    })

    // 在页面加载时运行日期转换测试
    testDateConversion()

    // 获取日历数据
    const getCalendarData = async () => {
      loading.value = true
      try {
        const [year, month] = currentDate.value.split('-').map(Number)
        
        const response = await proxy.$axios.post('/api/get_ops_calendar', {
          year,
          month
        })

        if (response.data.code === 0) {
          calendarData.value = response.data.msg
          console.log('获取到的日历数据总数:', response.data.msg.length)
          console.log('日历数据示例:', response.data.msg.slice(0, 3))

          // 检查变更数据
          const hasChanges = response.data.msg.filter(item => item.change_count > 0)
          console.log('有变更的日期:', hasChanges)

          // 检查数据类型
          if (hasChanges.length > 0) {
            console.log('变更数据类型检查:', {
              change_count: hasChanges[0].change_count,
              type: typeof hasChanges[0].change_count,
              day: hasChanges[0].day
            })
          }
        } else {
          ElMessage.error(`获取日历数据失败: ${response.data.msg}`)
        }
      } catch (error) {
        console.error('获取日历数据失败:', error)
        ElMessage.error('获取日历数据失败')
      } finally {
        loading.value = false
      }
    }

    // 获取指定日期的变更详情
    const getDateChanges = async (date) => {
      dialogLoading.value = true
      try {
        // 使用统一的日期解析函数
        const dateStr = parseToDBFormat(date)

        console.log(`获取变更详情，原始日期: ${date}, 转换后: ${dateStr}`)

        const response = await proxy.$axios.post('/api/get_ops_calendar_changes', {
          date: dateStr
        })

        if (response.data.code === 0) {
          selectedDateChanges.value = response.data.msg
        } else {
          ElMessage.error(`获取变更详情失败: ${response.data.msg}`)
        }
      } catch (error) {
        console.error('获取变更详情失败:', error)
        ElMessage.error('获取变更详情失败')
      } finally {
        dialogLoading.value = false
      }
    }

    // 获取日期样式类
    const getDayClass = (day) => {
      const classes = ['calendar-day']
      
      if (!day.isCurrentMonth) {
        classes.push('other-month')
      } else {
        if (day.isHoliday) {
          classes.push('holiday')
        } else if (day.isWorkday) {
          classes.push('workday')
        } else {
          classes.push('weekend')
        }
        
        if (day.changeCount > 0) {
          classes.push('has-change')
        }
      }
      
      return classes.join(' ')
    }

    // 处理日期变化
    const handleDateChange = () => {
      console.log('月份切换，保存选择的月份:', currentDate.value)
      // 保存选择的月份到localStorage
      localStorage.setItem('ops_calendar_selected_month', currentDate.value)
      // 重新获取日历数据，不再刷新页面
      getCalendarData()
    }

    // 刷新日历
    const refreshCalendar = () => {
      getCalendarData()
    }

    // 回到当前月
    const goToCurrentMonth = () => {
      const currentMonth = new Date().toISOString().slice(0, 7)
      console.log(`回到当前月: ${currentDate.value} -> ${currentMonth}`)

      if (currentDate.value !== currentMonth) {
        currentDate.value = currentMonth
        // 保存当前月到localStorage
        localStorage.setItem('ops_calendar_selected_month', currentMonth)
        // 重新加载数据，不再刷新页面
        getCalendarData()
      } else {
        ElMessage.info('当前已经是本月')
      }
    }

    // 上个月
    const goToPreviousMonth = () => {
      const [year, month] = currentDate.value.split('-').map(Number)
      let newYear = year
      let newMonth = month - 1

      if (newMonth < 1) {
        newMonth = 12
        newYear = year - 1
      }

      const newDate = `${newYear}-${newMonth.toString().padStart(2, '0')}`
      console.log(`切换到上个月: ${currentDate.value} -> ${newDate}`)
      currentDate.value = newDate

      // 保存选择的月份到localStorage
      localStorage.setItem('ops_calendar_selected_month', newDate)

      // 重新加载数据，不再刷新页面
      getCalendarData()
    }

    // 下个月
    const goToNextMonth = () => {
      const [year, month] = currentDate.value.split('-').map(Number)
      let newYear = year
      let newMonth = month + 1

      if (newMonth > 12) {
        newMonth = 1
        newYear = year + 1
      }

      const newDate = `${newYear}-${newMonth.toString().padStart(2, '0')}`
      console.log(`切换到下个月: ${currentDate.value} -> ${newDate}`)
      currentDate.value = newDate

      // 保存选择的月份到localStorage
      localStorage.setItem('ops_calendar_selected_month', newDate)

      // 重新加载数据，不再刷新页面
      getCalendarData()
    }

    // 处理日期点击 - 根据权限和内容决定行为
    const handleDayClick = (day) => {
      if (!day.isCurrentMonth) {
        return
      }

      selectedDate.value = day.date

      console.log('点击日期，当前权限状态:', {
        hasEditPermission: hasEditPermission.value,
        hasViewPermission: hasViewPermission.value,
        changeCount: day.changeCount
      })

      // 如果有变更，优先显示变更详情（所有用户都可以查看变更）
      if (day.changeCount > 0) {
        console.log(`点击日期: ${day.date}, 数据库日期: ${formatDateForDB(new Date(day.date))}`)
        dialogVisible.value = true
        getDateChanges(day.date)
      } else {
        // 如果没有变更，严格检查权限
        console.log('无变更日期，检查权限...')

        // 只有编辑权限的用户才能点击弹出值班排班表单
        if (hasEditPermission.value === true) {
          console.log('有编辑权限，显示值班排班对话框')
          showDutyDialog(day)
        } else {
          // 无编辑权限，根据查看权限给出不同提示
          console.log('无编辑权限，给出提示')
          if (hasViewPermission.value === true) {
            ElMessage.info('您只有查看权限，无法编辑值班排班。如需查看值班信息，请右键点击日期。')
          } else {
            ElMessage.warning('您没有交易日历的编辑权限')
          }
        }
      }
    }

    // 处理日期右键点击 - 根据权限显示值班排班
    const handleDayRightClick = (day) => {
      if (!day.isCurrentMonth) {
        return
      }

      console.log('右键点击日期，当前权限状态:', {
        hasEditPermission: hasEditPermission.value,
        hasViewPermission: hasViewPermission.value
      })

      // 严格检查权限：必须有查看权限或编辑权限
      if (hasViewPermission.value === true || hasEditPermission.value === true) {
        console.log('有权限，显示值班排班对话框')
        showDutyDialog(day)
      } else {
        console.log('无权限，拒绝访问')
        ElMessage.warning('您没有交易日历的编辑权限')
      }
    }

    // 显示值班排班对话框
    const showDutyDialog = (day) => {
      selectedDate.value = day.date

      // 填充现有的值班信息
      dutyForm.value = {
        main_duty_user: day.mainDutyUser || '',
        deputy_duty_user: day.deputyDutyUser || '',
        duty_manager: day.dutyManager || '',
        simulation_user: day.simulationUser || '',
        inspection_user: day.inspectionUser || ''
      }

      dutyDialogVisible.value = true
    }

    // 检查用户权限
    const checkUserPermission = async () => {
      permissionLoading.value = true

      // 先重置权限状态
      hasEditPermission.value = false
      hasViewPermission.value = false

      try {
        // 从localStorage获取当前登录用户名
        const currentUsername = localStorage.getItem('username') || localStorage.getItem('loginUsername') || 'admin'
        console.log('检查用户权限，用户名:', currentUsername)

        const response = await proxy.$axios.post('/api/check_ops_calendar_duty_permission', {
          username: currentUsername
        })

        console.log('权限检查API响应:', response.data)

        if (response.data.code === 0) {
          // 严格设置权限值
          hasEditPermission.value = Boolean(response.data.msg.hasEditPermission)
          hasViewPermission.value = Boolean(response.data.msg.hasViewPermission)

          console.log('用户权限检查结果:', {
            username: currentUsername,
            hasEditPermission: hasEditPermission.value,
            hasViewPermission: hasViewPermission.value,
            rawPermissions: response.data.msg
          })

          // 如果没有任何权限，给出提示
          if (!hasViewPermission.value && !hasEditPermission.value) {
            console.log('用户无任何权限')
            ElMessage.warning('您没有交易日历的编辑权限')
          } else {
            console.log('用户有权限:', {
              view: hasViewPermission.value,
              edit: hasEditPermission.value
            })
          }
        } else {
          console.error('权限检查API返回错误:', response.data.msg)
          ElMessage.error(`权限检查失败: ${response.data.msg}`)
          // 确保无权限
          hasEditPermission.value = false
          hasViewPermission.value = false
        }
      } catch (error) {
        console.error('权限检查请求失败:', error)
        ElMessage.error('权限检查失败')
        // 确保无权限
        hasEditPermission.value = false
        hasViewPermission.value = false
      } finally {
        permissionLoading.value = false
        console.log('权限检查完成，最终权限状态:', {
          hasEditPermission: hasEditPermission.value,
          hasViewPermission: hasViewPermission.value
        })
      }
    }

    // 获取用户列表
    const getUserList = async () => {
      try {
        const response = await proxy.$axios.post('/api/get_ops_calendar_users')

        if (response.data.code === 0) {
          userList.value = response.data.msg
        } else {
          ElMessage.error(`获取用户列表失败: ${response.data.msg}`)
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        ElMessage.error('获取用户列表失败')
      }
    }

    // 保存值班排班
    const saveDutySchedule = async () => {
      dutyLoading.value = true
      try {
        // 使用统一的日期解析函数
        const dateStr = parseToDBFormat(selectedDate.value)
        console.log(`保存值班排班，原始日期: ${selectedDate.value}, 转换后: ${dateStr}`)

        const response = await proxy.$axios.post('/api/update_ops_calendar_duty', {
          day: dateStr,
          main_duty_user: dutyForm.value.main_duty_user || null,
          deputy_duty_user: dutyForm.value.deputy_duty_user || null,
          duty_manager: dutyForm.value.duty_manager || null,
          simulation_user: dutyForm.value.simulation_user || null,
          inspection_user: dutyForm.value.inspection_user || null,
          updated_by: localStorage.getItem('username') || 'admin'
        })

        if (response.data.code === 0) {
          ElMessage.success('值班排班保存成功')
          dutyDialogVisible.value = false
          // 刷新日历数据
          getCalendarData()
        } else {
          ElMessage.error(`保存失败: ${response.data.msg}`)
        }
      } catch (error) {
        console.error('保存值班排班失败:', error)
        ElMessage.error('保存值班排班失败')
      } finally {
        dutyLoading.value = false
      }
    }

    // 查看变更详情
    const viewChangeDetail = (change) => {
      router.push(`/ops_change_management/detail/${change.id}`)
    }

    // 调试权限状态
    const debugPermissions = () => {
      const currentUsername = localStorage.getItem('username') || localStorage.getItem('loginUsername') || 'admin'

      ElMessage({
        message: `当前用户: ${currentUsername}\n编辑权限: ${hasEditPermission.value}\n查看权限: ${hasViewPermission.value}`,
        type: 'info',
        duration: 5000,
        dangerouslyUseHTMLString: true
      })

      console.log('=== 权限调试信息 ===')
      console.log('当前用户名:', currentUsername)
      console.log('localStorage中的用户名:', {
        username: localStorage.getItem('username'),
        loginUsername: localStorage.getItem('loginUsername')
      })
      console.log('权限状态:', {
        hasEditPermission: hasEditPermission.value,
        hasViewPermission: hasViewPermission.value,
        permissionLoading: permissionLoading.value
      })
      console.log('===================')
    }

    // 页面加载时执行
    onMounted(async () => {
      console.log('页面加载，开始检查权限...')
      await checkUserPermission() // 首先检查权限，等待完成
      console.log('权限检查完成，权限状态:', {
        hasEditPermission: hasEditPermission.value,
        hasViewPermission: hasViewPermission.value
      })
      getCalendarData()
      getUserList()
    })

    return {
      loading,
      dialogLoading,
      dialogVisible,
      dutyDialogVisible,
      dutyLoading,
      currentDate,
      calendarDays,
      selectedDate,
      selectedDateChanges,
      userList,
      dutyForm,
      currentMonthTitle,
      selectedDateFormatted,
      monthChangeCount,
      hasEditPermission,
      hasViewPermission,
      permissionLoading,
      Refresh,
      Calendar,
      getDayClass,
      handleDateChange,
      refreshCalendar,
      goToCurrentMonth,
      goToPreviousMonth,
      goToNextMonth,
      handleDayClick,
      handleDayRightClick,
      showDutyDialog,
      saveDutySchedule,
      viewChangeDetail,
      checkUserPermission,
      debugPermissions
    }
  }
}
</script>

<style lang="scss" scoped>
/* 页面容器 */
.calendar-container {
  padding: 20px;
}

/* 页面标题 - 统一样式 */
.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.operation-tips {
  margin-top: 12px;
}

.operation-tips .el-alert {
  border-radius: 4px;
}

.operation-tips .el-alert__content {
  font-size: 13px;
}

/* 卡片头部布局 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  padding: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 0 0 auto;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 0 0 auto;
}

.month-tag {
  margin-left: 8px;
}

/* 表单项样式 */
.form-item-inline {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

/* 月份选择器样式 */
.month-selector {
  display: flex;
  align-items: center;
  gap: 0;
}

.month-selector .el-button {
  border-radius: 4px;
  min-width: 32px;
  height: 32px;
  padding: 0;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.month-selector .el-button:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

.month-selector .el-button:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}

.month-selector .el-date-editor {
  border-radius: 0;
}

.month-selector .el-date-editor:deep(.el-input__wrapper) {
  border-radius: 0;
  border-left: none;
  border-right: none;
}

/* 图例样式 */
.legend {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #606266;
  padding: 4px 10px;
  border-radius: 4px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid #dcdfe6;
}

.legend-color.workday {
  background-color: #cde5fd;
  border-color: #409eff;
}

.legend-color.weekend {
  background-color: #fae3c1;
  border-color: #e6a23c;
}

.legend-color.has-change {
  background-color: #409eff;
  border-color: #409eff;
}

/* 表格卡片 - 统一样式 */
.table-card {
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 日历网格 */
.calendar-grid {
  width: 100%;
}

/* 星期标题 - 统一表格头样式 */
.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-bottom: 2px solid #ebeef5;
  background-color: #f5f7fa;
}

.week-day {
  padding: 12px;
  text-align: center;
  font-weight: bold;
  color: #606266;
  font-size: 14px;
  border-right: 1px solid #ebeef5;
}

.week-day:last-child {
  border-right: none;
}

/* 日历主体 */
.calendar-body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

/* 日历单元格 */
.calendar-day {
  min-height: 100px;
  border: 1px solid #ebeef5;
  border-top: none;
  border-left: none;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background-color: white;
}

.calendar-day:nth-child(7n) {
  border-right: none;
}

.calendar-day:hover {
  background-color: #f5f7fa;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 非当前月日期 */
.calendar-day.other-month {
  background-color: #fafafa;
  color: #c0c4cc;
  cursor: default;
}

.calendar-day.other-month:hover {
  background-color: #fafafa;
  box-shadow: none;
}

/* 工作日样式 */
.calendar-day.workday {
  background-color: #e0f0ff;
  border-color: #409eff;
}

/* 非工作日样式（周末和节假日） */
.calendar-day.weekend,
.calendar-day.holiday {
  background-color: #faf5e7;
  border-color: #ffe4b3;
}

/* 日期头部 */
.day-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

/* 日期数字 */
.day-number {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.calendar-day.other-month .day-number {
  color: #c0c4cc;
}

/* 变更角标 */
.change-badge-corner {
  position: absolute;
  top: -2px;
  right: -2px;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: white;
  font-size: 10px;
  font-weight: bold;
  min-width: 16px;
  height: 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(64, 158, 255, 0.3);
  border: 1px solid white;
  z-index: 2;
  cursor: pointer;
  transition: all 0.3s ease;
}

.change-badge-corner:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.5);
}

/* 日期内容区域 */
.day-content {
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 4px;
}

/* 值班信息样式 */
.duty-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 4px;
}

.duty-item {
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  color: white;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 简化颜色方案：只使用3种主色调 */
.duty-item.main-duty {
  background-color: #409eff; /* 蓝色 - 主要岗位 */
}

.duty-item.deputy-duty {
  background-color: #409eff; /* 蓝色 - 主要岗位 */
  opacity: 0.85;
}

.duty-item.manager-duty {
  background-color: #a14ce7; /* 紫色 - 管理岗位 */
}

.duty-item.simulation-duty {
  background-color: #909399; /* 灰色 - 辅助岗位 */
  opacity: 0.85;
}

.duty-item.inspection-duty {
  background-color: #909399; /* 灰色 - 辅助岗位 */
  opacity: 0.7;
}

/* 变更信息样式已移除，改为角标显示 */

/* 对话框样式 */
.dialog-content {
  padding: 0;
}

.dialog-header-info {
  margin-bottom: 16px;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 值班排班表单样式 */
.duty-form {
  padding: 20px 0;
}

.duty-form .el-form-item {
  margin-bottom: 20px;
}

.duty-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .card-header {
    flex-direction: column;
    gap: 12px;
  }

  .header-left,
  .header-center,
  .header-right {
    flex: none;
    width: 100%;
  }

  .header-center {
    order: 2;
  }

  .header-right {
    order: 3;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .calendar-container {
    padding: 10px;
  }

  .legend {
    gap: 12px;
    justify-content: center;
  }

  .legend-item {
    font-size: 12px;
    padding: 3px 6px;
  }

  .calendar-day {
    min-height: 80px;
    padding: 6px;
  }

  .day-number {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .change-summary {
    font-size: 10px;
    max-height: 30px;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .week-day {
    padding: 8px 4px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .calendar-container {
    padding: 8px;
  }

  .page-title {
    font-size: 20px;
  }

  .legend {
    gap: 8px;
  }

  .legend-item {
    font-size: 11px;
    padding: 2px 4px;
  }

  .legend-color {
    width: 12px;
    height: 12px;
  }

  .calendar-day {
    min-height: 60px;
    padding: 4px;
  }

  .day-number {
    font-size: 12px;
    margin-bottom: 2px;
  }

  .change-summary {
    font-size: 9px;
    max-height: 24px;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .week-day {
    padding: 6px 2px;
    font-size: 12px;
  }

  .change-badge {
    margin-bottom: 2px;
  }

  :deep(.el-badge__content) {
    font-size: 10px;
    padding: 0 4px;
    height: 16px;
    line-height: 16px;
  }
}

/* 按钮悬停效果 - 统一样式 */
:deep(.el-button) {
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表格样式优化 */
:deep(.el-table) {
  .el-table__header th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: bold;
  }

  .el-table__body tr:hover > td {
    background-color: #f5f7fa;
  }
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 8px;

  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #ebeef5;
  }
}
</style>
